package cn.coder.zj.module.collector.collect.token.zstack;

import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.hutool.core.convert.Convert;
import lombok.extern.slf4j.Slf4j;
import org.zstack.sdk.*;
import org.zstack.sdk.zwatch.api.GetMetricDataAction;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Supplier;

/**
 * ZStack客户端包装器
 * 解决ZStack SDK全局配置导致的多线程竞争问题
 * 提供线程安全的ZStack API调用
 * 
 * <AUTHOR>
 */
@Slf4j
public class ZStackClientWrapper {
    
    /**
     * 全局锁，确保ZSClient配置和API调用的原子性
     * 由于ZStack SDK使用全局静态配置，必须使用全局锁来保证线程安全
     */
    private static final ReentrantLock GLOBAL_LOCK = new ReentrantLock();
    
    /**
     * 平台配置缓存，避免重复配置相同平台
     * Key: platformId, Value: 配置标识(hostname:port)
     */
    private static final ConcurrentHashMap<Long, String> PLATFORM_CONFIG_CACHE = new ConcurrentHashMap<>();
    
    /**
     * 执行需要ZSClient的操作
     * 确保配置和API调用在同一个同步块内完成
     * 
     * @param platform 平台信息
     * @param operation 需要执行的操作
     * @param <T> 返回类型
     * @return 操作结果
     */
    public static <T> T executeWithClient(Platform platform, Supplier<T> operation) {
        GLOBAL_LOCK.lock();
        try {
            // 配置ZSClient
            configureClient(platform);
            
            // 执行操作
            return operation.get();
            
        } catch (Exception e) {
            log.error("平台 {} ZSClient操作执行失败: {}", platform.getPlatformName(), e.getMessage(), e);
            throw new RuntimeException("ZSClient操作执行失败", e);
        } finally {
            GLOBAL_LOCK.unlock();
        }
    }
    
    /**
     * 执行需要ZSClient的操作（无返回值版本）
     * 
     * @param platform 平台信息
     * @param operation 需要执行的操作
     */
    public static void executeWithClient(Platform platform, Runnable operation) {
        executeWithClient(platform, () -> {
            operation.run();
            return null;
        });
    }
    
    /**
     * 配置ZSClient
     * 
     * @param platform 平台信息
     */
    private static void configureClient(Platform platform) {
        try {
            String processedUrl = removeProtocolAndPort(platform.getPlatformUrl());
            String port = extractPort(platform.getPlatformUrl());
            String configKey = processedUrl + ":" + port;
            
            // 检查是否需要重新配置
            String cachedConfig = PLATFORM_CONFIG_CACHE.get(platform.getPlatformId());
            if (!configKey.equals(cachedConfig)) {
                log.debug("配置ZSClient: 平台={}, URL={}:{}", 
                    platform.getPlatformName(), processedUrl, port);
                
                ZSClient.configure(new ZSConfig.Builder()
                        .setHostname(processedUrl)
                        .setPort(Convert.toInt(port))
                        .setContextPath("zstack")
                        .build());
                
                // 更新缓存
                PLATFORM_CONFIG_CACHE.put(platform.getPlatformId(), configKey);
            }
        } catch (Exception e) {
            log.error("配置ZSClient失败: 平台={}, URL={}", 
                platform.getPlatformName(), platform.getPlatformUrl(), e);
            throw new RuntimeException("配置ZSClient失败", e);
        }
    }
    
    // ==================== 登录相关API包装方法 ====================

    /**
     * 用户名密码登录
     * 提供线程安全的登录接口，消除对外部ZSClient.configure的依赖
     *
     * @param platform 平台信息
     * @return 登录结果，包含token信息
     */
    public static LogInByAccountAction.Result loginByAccount(Platform platform) {
        return executeWithClient(platform, () -> {
            LogInByAccountAction action = new LogInByAccountAction();
            action.accountName = platform.getUsername();
            action.password = cn.coder.zj.module.collector.util.Sha256.SHA512(platform.getPassword());
            return action.call();
        });
    }

    /**
     * 验证登录状态
     * 通过尝试登录来验证平台连接性和认证信息的有效性
     *
     * @param platform 平台信息
     * @return 验证结果，true表示登录成功
     */
    public static boolean validateLogin(Platform platform) {
        try {
            LogInByAccountAction.Result result = loginByAccount(platform);
            return result != null &&
                   result.value != null &&
                   result.value.getInventory() != null &&
                   result.value.getInventory().uuid != null &&
                   !result.value.getInventory().uuid.trim().isEmpty();
        } catch (Exception e) {
            log.debug("平台 {} 登录验证失败: {}", platform.getPlatformName(), e.getMessage());
            return false;
        }
    }

    // ==================== 常用API包装方法 ====================

    /**
     * 查询VM实例
     */
    public static QueryVmInstanceAction.Result queryVmInstances(Platform platform) {
        return executeWithClient(platform, () -> {
            QueryVmInstanceAction action = new QueryVmInstanceAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }
    
    /**
     * 查询主机
     */
    public static QueryHostAction.Result queryHosts(Platform platform) {
        return executeWithClient(platform, () -> {
            QueryHostAction action = new QueryHostAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 查询主机（带条件）
     */
    public static QueryHostAction.Result queryHosts(Platform platform, List<String> conditions) {
        return executeWithClient(platform, () -> {
            QueryHostAction action = new QueryHostAction();
            if (conditions != null && !conditions.isEmpty()) {
                action.conditions = conditions;
            }
            setAuthentication(action, platform);
            return action.call();
        });
    }
    
    /**
     * 查询区域
     */
    public static QueryZoneAction.Result queryZones(Platform platform) {
        return executeWithClient(platform, () -> {
            QueryZoneAction action = new QueryZoneAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }
    
    /**
     * 查询集群
     */
    public static QueryClusterAction.Result queryClusters(Platform platform, List<String> conditions) {
        return executeWithClient(platform, () -> {
            QueryClusterAction action = new QueryClusterAction();
            if (conditions != null && !conditions.isEmpty()) {
                action.conditions = conditions;
            }
            setAuthentication(action, platform);
            return action.call();
        });
    }
    
    /**
     * 获取VM Guest Tools信息
     */
    public static GetVmGuestToolsInfoAction.Result getVmGuestToolsInfo(Platform platform, String vmUuid) {
        return executeWithClient(platform, () -> {
            GetVmGuestToolsInfoAction action = new GetVmGuestToolsInfoAction();
            action.uuid = vmUuid;
            setAuthentication(action, platform);
            return action.call();
        });
    }
    
    /**
     * 查询EIP
     */
    public static QueryEipAction.Result queryEips(Platform platform) {
        return executeWithClient(platform, () -> {
            QueryEipAction action = new QueryEipAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }
    
    /**
     * 查询用户标签
     */
    public static QueryUserTagAction.Result queryUserTags(Platform platform, List<String> conditions) {
        return executeWithClient(platform, () -> {
            QueryUserTagAction action = new QueryUserTagAction();
            if (conditions != null && !conditions.isEmpty()) {
                action.conditions = conditions;
            }
            setAuthentication(action, platform);
            return action.call();
        });
    }
    
    /**
     * 获取指标数据
     */
    public static GetMetricDataAction.Result getMetricData(Platform platform, String namespace, 
                                                          String metricName, List<String> labels, 
                                                          Long startTime, Long endTime) {
        return executeWithClient(platform, () -> {
            GetMetricDataAction action = new GetMetricDataAction();
            action.namespace = namespace;
            action.metricName = metricName;
            action.labels = labels;
            if (startTime != null) {
                action.startTime = startTime;
            }
            if (endTime != null) {
                action.endTime = endTime;
            }
            setAuthentication(action, platform);
            return action.call();
        });
    }
    
    /**
     * 查询存储卷
     */
    public static QueryVolumeAction.Result queryVolumes(Platform platform, List<String> conditions) {
        return executeWithClient(platform, () -> {
            QueryVolumeAction action = new QueryVolumeAction();
            if (conditions != null && !conditions.isEmpty()) {
                action.conditions = conditions;
            }
            setAuthentication(action, platform);
            return action.call();
        });
    }
    
    /**
     * 查询主存储
     */
    public static QueryPrimaryStorageAction.Result queryPrimaryStorages(Platform platform) {
        return executeWithClient(platform, () -> {
            QueryPrimaryStorageAction action = new QueryPrimaryStorageAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 查询镜像
     */
    public static QueryImageAction.Result queryImages(Platform platform) {
        return executeWithClient(platform, () -> {
            QueryImageAction action = new QueryImageAction();
            action.conditions = new ArrayList<>();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 查询镜像（带条件）
     */
    public static QueryImageAction.Result queryImages(Platform platform, List<String> conditions) {
        return executeWithClient(platform, () -> {
            QueryImageAction action = new QueryImageAction();
            if (conditions != null) {
                action.conditions = conditions;
            } else {
                action.conditions = new ArrayList<>();
            }
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 查询L2网络
     */
    public static QueryL2NetworkAction.Result queryL2Networks(Platform platform) {
        return executeWithClient(platform, () -> {
            QueryL2NetworkAction action = new QueryL2NetworkAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 查询L2网络（带条件）
     */
    public static QueryL2NetworkAction.Result queryL2Networks(Platform platform, List<String> conditions) {
        return executeWithClient(platform, () -> {
            QueryL2NetworkAction action = new QueryL2NetworkAction();
            if (conditions != null && !conditions.isEmpty()) {
                action.conditions = conditions;
            }
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 查询L3网络
     */
    public static QueryL3NetworkAction.Result queryL3Networks(Platform platform) {
        return executeWithClient(platform, () -> {
            QueryL3NetworkAction action = new QueryL3NetworkAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 查询L3网络（带条件）
     */
    public static QueryL3NetworkAction.Result queryL3Networks(Platform platform, List<String> conditions) {
        return executeWithClient(platform, () -> {
            QueryL3NetworkAction action = new QueryL3NetworkAction();
            if (conditions != null && !conditions.isEmpty()) {
                action.conditions = conditions;
            }
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 查询VPC路由器
     */
    public static QueryVpcRouterAction.Result queryVpcRouters(Platform platform) {
        return executeWithClient(platform, () -> {
            QueryVpcRouterAction action = new QueryVpcRouterAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 查询VPC路由器（带条件）
     */
    public static QueryVpcRouterAction.Result queryVpcRouters(Platform platform, List<String> conditions) {
        return executeWithClient(platform, () -> {
            QueryVpcRouterAction action = new QueryVpcRouterAction();
            if (conditions != null && !conditions.isEmpty()) {
                action.conditions = conditions;
            }
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 查询Ceph主存储池
     */
    public static QueryCephPrimaryStoragePoolAction.Result queryCephPrimaryStoragePools(Platform platform) {
        return executeWithClient(platform, () -> {
            QueryCephPrimaryStoragePoolAction action = new QueryCephPrimaryStoragePoolAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 查询快照
     */
    public static QueryVolumeSnapshotAction.Result queryVolumeSnapshots(Platform platform) {
        return executeWithClient(platform, () -> {
            QueryVolumeSnapshotAction action = new QueryVolumeSnapshotAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 查询安全组
     */
    public static QuerySecurityGroupAction.Result querySecurityGroups(Platform platform) {
        return executeWithClient(platform, () -> {
            QuerySecurityGroupAction action = new QuerySecurityGroupAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 查询主机网络接口
     */
    public static QueryHostNetworkInterfaceAction.Result queryHostNetworkInterfaces(Platform platform, List<String> conditions) {
        return executeWithClient(platform, () -> {
            QueryHostNetworkInterfaceAction action = new QueryHostNetworkInterfaceAction();
            if (conditions != null && !conditions.isEmpty()) {
                action.conditions = conditions;
            }
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 查询全局配置
     */
    public static QueryGlobalConfigAction.Result queryGlobalConfigs(Platform platform) {
        return executeWithClient(platform, () -> {
            QueryGlobalConfigAction action = new QueryGlobalConfigAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 获取资源配置
     */
    public static GetResourceConfigAction.Result getResourceConfig(Platform platform, String category, String name, String resourceUuid) {
        return executeWithClient(platform, () -> {
            GetResourceConfigAction action = new GetResourceConfigAction();
            action.category = category;
            action.name = name;
            action.resourceUuid = resourceUuid;
            setAuthentication(action, platform);
            return action.call();
        });
    }
    
    // ==================== 辅助方法 ====================

    /**
     * 为Action设置认证信息
     */
    private static void setAuthentication(Object action, Platform platform) {
        try {
            if (platform.getAkType() == 0) {
                // sessionId认证
                String token = platform.getZsTackPlatform().getToken();
                action.getClass().getField("sessionId").set(action, token);
            } else {
                // Access Key认证
                action.getClass().getField("accessKeyId").set(action, platform.getUsername());
                action.getClass().getField("accessKeySecret").set(action, platform.getPassword());
            }
        } catch (Exception e) {
            log.error("设置认证信息失败: {}", e.getMessage(), e);
            throw new RuntimeException("设置认证信息失败", e);
        }
    }

    /**
     * 从URL中移除协议和端口，提取主机名
     * 从ZsTackTokenImpl中移植的工具方法
     */
    private static String removeProtocolAndPort(String url) {
        if (url == null || url.trim().isEmpty()) {
            throw new IllegalArgumentException("URL不能为空");
        }

        // 移除协议部分
        String result = url.replaceFirst("^https?://", "");

        // 移除端口部分
        int colonIndex = result.indexOf(':');
        if (colonIndex != -1) {
            result = result.substring(0, colonIndex);
        }

        // 移除路径部分
        int slashIndex = result.indexOf('/');
        if (slashIndex != -1) {
            result = result.substring(0, slashIndex);
        }

        return result;
    }

    /**
     * 从URL中提取端口号
     * 从ZsTackTokenImpl中移植的工具方法
     */
    private static String extractPort(String url) {
        if (url == null || url.trim().isEmpty()) {
            return "8080"; // 默认端口
        }

        // 移除协议部分
        String withoutProtocol = url.replaceFirst("^https?://", "");

        // 查找端口
        int colonIndex = withoutProtocol.indexOf(':');
        if (colonIndex != -1) {
            int slashIndex = withoutProtocol.indexOf('/', colonIndex);
            if (slashIndex != -1) {
                return withoutProtocol.substring(colonIndex + 1, slashIndex);
            } else {
                return withoutProtocol.substring(colonIndex + 1);
            }
        }

        return "8080"; // 默认端口
    }
    

    

    
    /**
     * 清理平台配置缓存
     */
    public static void clearPlatformCache(Long platformId) {
        PLATFORM_CONFIG_CACHE.remove(platformId);
        log.debug("清理平台配置缓存: platformId={}", platformId);
    }
    
    /**
     * 清理所有配置缓存
     */
    public static void clearAllCache() {
        PLATFORM_CONFIG_CACHE.clear();
        log.debug("清理所有平台配置缓存");
    }
}
