package cn.coder.zj.module.collector.collect.token.zstack;

import cn.coder.zj.module.collector.collect.token.AbstractToken;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.dal.platform.common.MonitorInfo;
import cn.coder.zj.module.collector.dal.platform.zstack.ZsTackPlatform;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.NetworkUtil;
import cn.coder.zj.module.collector.util.Sha256;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.convert.Convert;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.AsyncTaskExecutor;
import org.zstack.sdk.*;

import java.util.*;
import java.util.concurrent.FutureTask;
import java.util.concurrent.TimeUnit;

import static cn.coder.zj.module.collector.enums.PlatformType.ZS_TACK;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
import static cn.coder.zj.module.collector.util.PortUtil.extractPort;
import static cn.coder.zj.module.collector.util.PortUtil.removeProtocolAndPort;


@Slf4j
public class ZsTackTokenImpl extends AbstractToken {


    @Override
    public synchronized void preCheck(Platform platform) {
        AsyncTaskExecutor asyncTaskExecutor = SpringBeanUtils.getBean(AsyncTaskExecutor.class);
        final int MAX_ATTEMPTS = 3;
        int successCount = 0;
        int failureCount = 0;

        log.info("开始对平台 {} 进行连接检查，将尝试 {} 次", platform.getPlatformName(), MAX_ATTEMPTS);

        for (int attempt = 1; attempt <= MAX_ATTEMPTS; attempt++) {
            int finalAttempt = attempt;
            FutureTask<Boolean> task = new FutureTask<>(() -> {
                if (platform.getAkType() == 0) {
                    try {
                        String processedUrl = removeProtocolAndPort(platform.getPlatformUrl());
                        String port = extractPort(platform.getPlatformUrl());

                        // 1. 首先进行网络连通性预检查
                        if (!NetworkUtil.isTcpPortOpen(processedUrl, Convert.toInt(port))) {
                            log.debug("平台 {} 第{}次尝试: 网络不可达 {}:{}",
                                    platform.getPlatformName(), finalAttempt, processedUrl, port);
                            return false;
                        }

                        // 2. 使用ZStackClientWrapper进行登录验证，消除ZSClient.configure依赖
                        boolean loginSuccess = ZStackClientWrapper.validateLogin(platform);

                        // 3. 验证登录结果
                        if (loginSuccess) {
                            log.debug("平台 {} 第{}次尝试验证通过", platform.getPlatformName(), finalAttempt);
                            return true;
                        } else {
                            log.debug("平台 {} 第{}次尝试: 登录验证失败", platform.getPlatformName(), finalAttempt);
                            return false;
                        }

                    } catch (Exception e) {
                        log.debug("平台 {} 第{}次尝试异常: {}", platform.getPlatformName(), finalAttempt, e.getMessage());
                        return false;
                    }
                } else {
                    // SDK方法：使用accessKey验证，通过查询VM来验证连接性
                    try {
                        List<MonitorInfo> vmUuids = getUuids(platform, "VM");
                        if (vmUuids.size() > 0) {
                            log.debug("平台 {} 第{}次尝试验证通过，VM数量: {}",
                                    platform.getPlatformName(), finalAttempt, vmUuids.size());
                            return true;
                        }
                        log.debug("平台 {} 第{}次尝试: 未查询到VM", platform.getPlatformName(), finalAttempt);
                        return false;
                    } catch (Exception e) {
                        log.debug("平台 {} 第{}次尝试异常: {}", platform.getPlatformName(), finalAttempt, e.getMessage());
                        return false;
                    }
                }


            });

            try {
                asyncTaskExecutor.submit(task);
                boolean isConnected = task.get(10, TimeUnit.SECONDS);

                if (isConnected) {
                    successCount++;
                    log.info("平台 {} 第{}次连接检查成功", platform.getPlatformName(), attempt);
                } else {
                    failureCount++;
                    log.warn("平台 {} 第{}次连接检查失败", platform.getPlatformName(), attempt);
                }

            } catch (Exception e) {
                failureCount++;
                log.error("平台 {} 第{}次连接检查超时", platform.getPlatformName(), attempt);
                task.cancel(true);
            }

            // 继续所有尝试，不提前退出
            if (attempt < MAX_ATTEMPTS) {
                try {
                    // 两次尝试之间等待一小段时间，确保在一分钟内完成3次尝试
                    Thread.sleep(15000); // 等待15秒
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }

        // 根据用户偏好：基于最终结果评估成功/失败，而不是任何单次成功
        // 如果3次连接尝试都超时，直接判断为超时，不使用复杂的异步任务管理
        boolean finalResult = successCount > 0;

        if (finalResult) {
            log.info("平台 {} 连接检查最终结果: 成功 (成功次数: {}/{})",
                    platform.getPlatformName(), successCount, MAX_ATTEMPTS);
        } else {
            if (failureCount == MAX_ATTEMPTS) {
                log.error("平台 {} 连接检查最终结果: 全部失败，判断为超时 (失败次数: {}/{})",
                        platform.getPlatformName(), failureCount, MAX_ATTEMPTS);
            } else {
                log.error("平台 {} 连接检查最终结果: 失败 (失败次数: {}/{})",
                        platform.getPlatformName(), failureCount, MAX_ATTEMPTS);
            }
        }

        // 在所有尝试结束后更新平台状态
        updatePlatformStatus(platform, finalResult);
        // {{END_MODIFICATIONS}}
    }


    @Override
    public synchronized void token(Platform platform) {
        try {
            if (platform.getAkType() == 0) {
                // AK方法：使用用户名密码登录获取token
                // 使用ZStackClientWrapper的登录接口，消除ZSClient.configure依赖
                LogInByAccountAction.Result res;
                try {
                    res = ZStackClientWrapper.loginByAccount(platform);
                    if (res == null || res.value == null || res.value.getInventory() == null) {
                        log.error("平台 {} 登录失败: 返回结果为空", platform.getPlatformName());
                        updatePlatformStatus(platform, false);
                        return;
                    }
                    String token = res.value.getInventory().uuid;
                    platform.setZsTackPlatform(ZsTackPlatform.builder().token(token).build());
                    List<MonitorInfo> vmUuids = getUuids(platform, "VM");
                    List<MonitorInfo> hostUuids = getUuids(platform, "HOST");
                    platform.setZsTackPlatform(ZsTackPlatform.builder()
                            .token(token)
                            .vmUuids(vmUuids)
                            .hostUuids(hostUuids)
                            .type(platform.getAkType())
                            .build());

                    Map<String, Object> map = new HashMap<>();
                    map.put(platform.getPlatformId().toString(), platform);
                    CacheService.put(ZS_TACK.code(), map);

                    log.info("平台 {} AK方法token获取成功，token: {}, VM数量: {}, Host数量: {}",
                            platform.getPlatformName(),
                            token.substring(0, Math.min(8, token.length())) + "...",
                            vmUuids.size(), hostUuids.size());

                } catch (Exception e) {
                    log.error("平台 {} AK方法token获取失败", platform.getPlatformName(), e);
                    updatePlatformStatus(platform, false);
                }
            } else {
                // SDK方法：使用accessKey直接调用API
                try {
                    List<MonitorInfo> vmUuids = getUuids(platform, "VM");
                    List<MonitorInfo> hostUuids = getUuids(platform, "HOST");
                    platform.setZsTackPlatform(ZsTackPlatform.builder()
                            .token("")
                            .vmUuids(vmUuids)
                            .hostUuids(hostUuids)
                            .type(platform.getAkType())
                            .build());

                    Map<String, Object> map = new HashMap<>();
                    map.put(platform.getPlatformId().toString(), platform);
                    CacheService.put(ZS_TACK.code(), map);

                    log.info("平台 {} SDK方法token获取成功，VM数量: {}, Host数量: {}",
                            platform.getPlatformName(), vmUuids.size(), hostUuids.size());

                } catch (Exception e) {
                    log.error("平台 {} SDK方法token获取失败", platform.getPlatformName(), e);
                    updatePlatformStatus(platform, false);
                }
            }

        } catch (Exception e) {
            log.error("平台 {} token获取过程发生异常", platform.getPlatformName(), e);
            updatePlatformStatus(platform, false);
        }
        // {{END_MODIFICATIONS}}
    }

    /**
     * 发送平台异常信息
     */
    private void updatePlatformStatus(Platform platform, boolean isOnline) {
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        if (isOnline) {
            platform.setState(0L);
        } else {
            platform.setState(1L);
        }
        platform.setDateTime(new Date());
        sendMessageService.sendMessage(CacheService.getCtx("ctx"), ClusterMsg.Message.newBuilder().setData(GsonUtil.GSON.toJson(platform)).setType(ClusterMsg.MessageType.DETECT).setTime(System.currentTimeMillis()).build());
    }

    /**
     * 获取VM或Host的UUID列表
     * 注意：此方法假设ZSClient已经在调用线程中正确配置
     * 应该在synchronized块内调用以确保线程安全
     */
    public List<MonitorInfo> getUuids(Platform platform, String actionType) {
        List<MonitorInfo> uuidList = new ArrayList<>();
        List<?> inventories;

        try {
            if ("VM".equals(actionType)) {
                QueryVmInstanceAction.Result result = ZStackClientWrapper.queryVmInstances(platform);
                inventories = result.value.inventories;
            } else if ("HOST".equals(actionType)) {
                QueryHostAction.Result result = ZStackClientWrapper.queryHosts(platform);
                inventories = result.value.inventories;
            } else {
                throw new IllegalArgumentException("Invalid action type: " + actionType);
            }

            // 解析返回的数据，只保留KVM类型的资源
            for (Object o : inventories) {
                JsonObject jsonObject = GSON.toJsonTree(o).getAsJsonObject();
                JsonElement hypervisorType = jsonObject.get("hypervisorType");
                JsonElement uuid = jsonObject.get("uuid");
                JsonElement name = jsonObject.get("name");

                if (hypervisorType != null && !hypervisorType.isJsonNull() &&
                        uuid != null && !uuid.isJsonNull() &&
                        "KVM".equals(hypervisorType.getAsString())) {
                    uuidList.add(MonitorInfo.builder()
                            .name(name.getAsString())
                            .uuid(uuid.getAsString())
                            .build());
                }
            }

            log.debug("平台 {} 获取{}列表成功，数量: {}", platform.getPlatformName(), actionType, uuidList.size());

        } catch (Exception e) {
            log.error("平台 {} 获取{}列表失败", platform.getPlatformName(), actionType, e);
        }

        return uuidList;
    }


    @Override
    public String supportProtocol() {
        return ZS_TACK.code();
    }






}
