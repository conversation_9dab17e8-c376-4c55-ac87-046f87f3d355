package cn.coder.zj.module.collector.job.task;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.collect.metrics.AbstractMetrics;
import cn.coder.zj.module.collector.collect.strategy.BasicStrategyFactory;
import cn.coder.zj.module.collector.collect.strategy.CollectStrategyFactory;
import cn.coder.zj.module.collector.job.annotation.YQJob;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Component;

import java.util.List;

import static cn.coder.zj.module.collector.enums.CpuType.getAllCpuTypeCodes;
import static cn.coder.zj.module.collector.enums.DiskType.getAllDiskTypeCodes;
import static cn.coder.zj.module.collector.enums.MemType.getAllMemTypeCodes;
import static cn.coder.zj.module.collector.enums.NetType.getAllNeTypeCodes;
import static cn.iocoder.zj.framework.common.enums.BasicType.getAllBasicTypeCodes;


@Slf4j
@Component
public class TaskService {


    private final TaskExecutor taskExecutor;

    public TaskService(TaskExecutor taskExecutor) {
        this.taskExecutor = taskExecutor;
    }


    /**
     * 处理基础信息
     */
    @YQJob(value ="basic_info", cronExpression = "0/600 * * * * ?")
    public void processBasicData(ClusterMsg.Message.Builder message) {
        List<String> typeCodes = getAllBasicTypeCodes();
        for (String typeCode : typeCodes) {
            taskExecutor.execute(() -> {
                AbstractBasicData abstractCollect = BasicStrategyFactory.invoke(typeCode);
                if (abstractCollect == null) {
                    log.info("跳过未实现的采集类型: {}", typeCode);
                    return;
                }
                abstractCollect.collectBasicData(message);
            });
        }
    }

    /**
     * 处理cpu性能信息
     */
    @YQJob(value ="cpu_info", cronExpression = "0/60 * * * * ?")
    public void processCpuData(ClusterMsg.Message.Builder message) {
        List<String> typeCodes = getAllCpuTypeCodes();
        for (String typeCode : typeCodes) {
            AbstractMetrics abstractCollect = CollectStrategyFactory.invoke(typeCode);
            abstractCollect.collectData(message);
        }
    }

    /**
     * 处理mem性能信息
     */
    @YQJob(value ="mem_info", cronExpression = "0/60 * * * * ?")
    public void processMemData(ClusterMsg.Message.Builder message) {
        List<String> typeCodes = getAllMemTypeCodes();
        for (String typeCode : typeCodes) {
            AbstractMetrics abstractCollect = CollectStrategyFactory.invoke(typeCode);
            abstractCollect.collectData(message);
        }
    }

    /**
     * 处理disk性能信息
     */
    @YQJob(value ="disk_info", cronExpression = "0/60 * * * * ?")
    public void processDiskData(ClusterMsg.Message.Builder message) {
        List<String> typeCodes = getAllDiskTypeCodes();
        for (String typeCode : typeCodes) {
            AbstractMetrics abstractCollect = CollectStrategyFactory.invoke(typeCode);
            abstractCollect.collectData(message);
        }
    }

    /**
     * 处理Net性能信息
     */
    @YQJob(value ="net_info", cronExpression = "0/60 * * * * ?")
    public void processNetData(ClusterMsg.Message.Builder message) {
        List<String> typeCodes = getAllNeTypeCodes();
        for (String typeCode : typeCodes) {
            AbstractMetrics abstractCollect = CollectStrategyFactory.invoke(typeCode);
            abstractCollect.collectData(message);
        }
    }

}